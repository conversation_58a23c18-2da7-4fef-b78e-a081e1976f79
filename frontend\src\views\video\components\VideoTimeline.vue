<template>
  <div class="video-timeline-container" ref="timelineContainer">
    <canvas ref="timelineCanvas" @click="handleCanvasClick"></canvas>
    <div
      v-if="tooltip.visible"
      class="tooltip"
      :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
    >
      {{ tooltip.text }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const props = defineProps({
  segments: {
    type: Array,
    required: true,
  },
  selectedDate: {
    type: Date,
    required: true,
  },
});

const emit = defineEmits(['time-selected']);

const timelineContainer = ref(null);
const timelineCanvas = ref(null);
const tooltip = ref({ visible: false, x: 0, y: 0, text: '' });

let canvasCtx = null;
let canvasWidth = 0;
let canvasHeight = 60; // 默认高度

function drawTimeline() {
  if (!canvasCtx) return;

  const startOfDay = dayjs(props.selectedDate).startOf('day');
  const endOfDay = dayjs(props.selectedDate).endOf('day');
  const totalSecondsInDay = endOfDay.diff(startOfDay, 'second');

  // 清空画布
  canvasCtx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 绘制背景
  canvasCtx.fillStyle = '#f0f2f5';
  canvasCtx.fillRect(0, 0, canvasWidth, canvasHeight);

  // 绘制时间刻度
  canvasCtx.fillStyle = '#999';
  canvasCtx.font = '12px Arial';
  for (let hour = 0; hour < 24; hour++) {
    const x = (hour / 24) * canvasWidth;
    canvasCtx.fillText(`${hour}:00`, x + 5, 15);
    canvasCtx.beginPath();
    canvasCtx.moveTo(x, 20);
    canvasCtx.lineTo(x, canvasHeight);
    canvasCtx.strokeStyle = '#ccc';
    canvasCtx.stroke();
  }

  // 绘制视频片段
  props.segments.forEach(segment => {
    const segmentStart = dayjs.utc(segment.segment_start_time);
    const offsetSeconds = segmentStart.diff(startOfDay.utc(), 'second');
    
    const x = (offsetSeconds / totalSecondsInDay) * canvasWidth;
    const width = (segment.duration / totalSecondsInDay) * canvasWidth;

    canvasCtx.fillStyle = '#409EFF';
    canvasCtx.fillRect(x, 25, width, canvasHeight - 30);
  });
}

function handleCanvasClick(event) {
  const rect = timelineCanvas.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  
  const startOfDay = dayjs(props.selectedDate).startOf('day');
  const endOfDay = dayjs(props.selectedDate).endOf('day');
  const totalSecondsInDay = endOfDay.diff(startOfDay, 'second');

  const clickedSecond = Math.floor((x / canvasWidth) * totalSecondsInDay);
  const clickedTime = startOfDay.add(clickedSecond, 'second');

  // 查找对应的视频片段
  const targetSegment = props.segments.find(segment => {
    const segmentStart = dayjs.utc(segment.segment_start_time).local();
    const segmentEnd = segmentStart.add(segment.duration, 'second');
    return clickedTime.isAfter(segmentStart) && clickedTime.isBefore(segmentEnd);
  });

  if (targetSegment) {
    const segmentStartTime = dayjs.utc(targetSegment.segment_start_time).local();
    const offsetSeconds = clickedTime.diff(segmentStartTime, 'second');
    emit('time-selected', { segment: targetSegment, offsetSeconds });
  }
}

function setupCanvas() {
    if (timelineContainer.value && timelineCanvas.value) {
        canvasWidth = timelineContainer.value.offsetWidth;
        timelineCanvas.value.width = canvasWidth;
        timelineCanvas.value.height = canvasHeight;
        canvasCtx = timelineCanvas.value.getContext('2d');
        drawTimeline();
    }
}

onMounted(() => {
    nextTick(() => {
        setupCanvas();
        window.addEventListener('resize', setupCanvas);
    });
});

watch(() => [props.segments, props.selectedDate], () => {
    nextTick(() => {
        drawTimeline();
    });
}, { deep: true });

</script>

<style scoped>
.video-timeline-container {
  width: 100%;
  height: 60px;
  position: relative;
  cursor: pointer;
}
canvas {
  display: block;
}
.tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  transform: translate(-50%, -110%);
}
</style>