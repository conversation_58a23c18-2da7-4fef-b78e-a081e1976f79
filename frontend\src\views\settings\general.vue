<template>
  <div class="general-settings">
    
    <v-card class="pa-4 mx-auto w-[700px]" variant="flat">
      <div class="d-flex flex-column gap-6">
        <!-- 手柄指示器 -->
        <div class="d-flex align-center justify-space-between">
          <span class="text-h6">手柄指示器</span>
          <v-checkbox
            v-model="formData.handleIndicator"
            hide-details
            density="compact"
            color="primary"
            class="setting-control"
            @change="sendParams2Android('android.showViewParams.showRockerView', formData.handleIndicator, curPage)"
          ></v-checkbox>
        </div>

        <!-- 左手柄XY轴切换 -->
        <div class="d-flex align-center justify-space-between">
          <span class="text-h6">左手柄XY轴切换</span>
          <v-checkbox
            v-model="formData.leftJoystickSwitch"
            hide-details
            density="compact"
            color="primary"
            class="setting-control"
            @change="
              sendParams2Android(
                'android.switchExtendedFunctionParams.leftHandleReversal',
                formData.leftJoystickSwitch,
                curPage
              )
            "
          ></v-checkbox>
        </div>

        <!-- 下次开机可调整语言 -->
        <div class="d-flex align-center justify-space-between">
          <span class="text-h6">下次开机可调整语言</span>
          <v-checkbox
            v-model="formData.changeLanguage"
            hide-details
            density="compact"
            color="primary"
            class="setting-control"
            @change="sendParams2Android('localAndroid.showLanguageNext', formData.changeLanguage, curPage)"
          ></v-checkbox>
        </div>

        <!-- 网络波动记录阈值 -->
        <div class="d-flex align-center justify-space-between">
          <span class="text-h6">网络波动记录阈值</span>
          <div class="d-flex align-center">
            <v-text-field
              v-model="formData.networkThreshold"
              type="number"
              density="compact"
              hide-details
              class="threshold-input"
              style="width: 100px"
              suffix="ms"
            ></v-text-field>
            <v-btn color="primary" variant="text" class="ms-2 text-h6" @click="saveThreshold"> 保存 </v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted } from "vue";
import { sendParams2Android } from "@/utils/androidMessage";
import { ipc } from "@/utils/ipcRenderer";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";

const curPage = 250;

const formData = reactive({
  handleIndicator: false,
  leftJoystickSwitch: false,
  changeLanguage: false,
  networkThreshold: 50,
});

const saveThreshold = () => {
  sendParams2Android("android.networkStatus.pingWarnValue", formData.networkThreshold, curPage);
};



// 处理WebSocket消息
const handleWsMessage = (event, msg) => {
  console.log("收到Android消息:", msg);
};

const initializeSettings = async () => {
  try {
    formData.handleIndicator = await ipc.invoke("global-state:get", "android.showViewParams.showRockerView");
    formData.leftJoystickSwitch = await ipc.invoke(
      "global-state:get",
      "android.switchExtendedFunctionParams.leftHandleReversal"
    );
    formData.changeLanguage = await ipc.invoke("global-state:get", "localAndroid.showLanguageNext");
    formData.networkThreshold = await ipc.invoke("global-state:get", "android.networkStatus.pingWarnValue");
  } catch (error) {
    console.error("获取设备数据显示配置失败:", error);
  }
};

let handlerId = null;

onMounted(async () => {
  // 注册WebSocket消息处理函数
  handlerId = registerWsHandler(250, handleWsMessage);
  initializeSettings();
});

// 组件卸载时注销消息处理函数
onUnmounted(() => {
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
});
</script>

<style scoped>
.general-settings {
  height: 100%;
  padding: 16px;
}

.threshold-input :deep(.v-field__input) {
  font-size: 1.25rem;
}
</style>
