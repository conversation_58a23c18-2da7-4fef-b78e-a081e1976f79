<template>
  <v-card class="connection-monitor" elevation="2">
    <v-card-title class="d-flex align-center">
      <v-icon :color="statusColor" class="mr-2">{{ statusIcon }}</v-icon>
      <span>双连接状态监控</span>
      <v-spacer></v-spacer>
      <v-btn
        icon
        size="small"
        @click="refreshStatus"
        :loading="loading"
      >
        <v-icon>mdi-refresh</v-icon>
      </v-btn>
    </v-card-title>

    <v-card-text>
      <!-- 服务状态 -->
      <v-row class="mb-4">
        <v-col cols="12">
          <v-alert
            :type="serviceStatus.isStarted ? 'success' : 'warning'"
            variant="tonal"
            class="mb-3"
          >
            <div class="d-flex align-center">
              <strong>服务状态：</strong>
              <span class="ml-2">
                {{ serviceStatus.isStarted ? '运行中' : '已停止' }}
              </span>
              <v-spacer></v-spacer>
              <v-chip
                :color="serviceStatus.isStarted ? 'success' : 'warning'"
                size="small"
                variant="flat"
              >
                {{ serviceStatus.isStarted ? '正常' : '停止' }}
              </v-chip>
            </div>
          </v-alert>
        </v-col>
      </v-row>

      <!-- 连接详情 -->
      <v-row>
        <v-col cols="12" md="6">
          <v-card variant="outlined" class="connection-card">
            <v-card-title class="d-flex align-center">
              <v-icon color="primary" class="mr-2">mdi-wifi</v-icon>
              <span>WebSocket 连接</span>
              <v-spacer></v-spacer>
              <v-chip
                :color="getStateColor(websocketConnection.isConnected)"
                size="small"
                variant="flat"
              >
                {{ getStateText(websocketConnection.isConnected) }}
              </v-chip>
            </v-card-title>
            <v-card-text>
              <div class="connection-info">
                <div class="info-item">
                  <span class="label">状态：</span>
                  <span :class="getStateClass(websocketConnection.isConnected)">
                    {{ getStateText(websocketConnection.isConnected) }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">URL：</span>
                  <span class="text-caption">{{ websocketConnection.url || '未配置' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">重连次数：</span>
                  <span>{{ websocketConnection.reconnectAttempts || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="label">最后消息：</span>
                  <span class="text-caption">{{ formatTime(websocketConnection.lastMessageTime) }}</span>
                </div>
              </div>
            </v-card-text>
           <v-card-actions>
             <v-spacer></v-spacer>
             <v-btn
               color="error"
               variant="text"
               @click="stopWebsocket"
               :disabled="!websocketConnection.isConnected"
             >
               停止
             </v-btn>
           </v-card-actions>
          </v-card>
        </v-col>

        <v-col cols="12" md="6">
          <v-card variant="outlined" class="connection-card">
            <v-card-title class="d-flex align-center">
              <v-icon color="orange" class="mr-2">mdi-usb</v-icon>
              <span>USB 连接</span>
              <v-spacer></v-spacer>
              <v-chip
                :color="getStateColor(usbConnection.isConnected)"
                size="small"
                variant="flat"
              >
                {{ getStateText(usbConnection.isConnected) }}
              </v-chip>
            </v-card-title>
            <v-card-text>
              <div class="connection-info">
                <div class="info-item">
                  <span class="label">状态：</span>
                  <span :class="getStateClass(usbConnection.isConnected)">
                    {{ getStateText(usbConnection.isConnected) }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="label">端口：</span>
                  <span class="text-caption">{{ usbConnection.portPath || '未连接' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">重连次数：</span>
                  <span>{{ usbConnection.reconnectAttempts || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="label">最后消息：</span>
                  <span class="text-caption">{{ formatTime(usbConnection.lastMessageTime) }}</span>
                </div>
              </div>
            </v-card-text>
           <v-card-actions>
             <v-spacer></v-spacer>
             <v-btn
               color="error"
               variant="text"
               @click="stopUsb"
               :disabled="!usbConnection.isConnected"
             >
               停止
             </v-btn>
           </v-card-actions>
          </v-card>
        </v-col>
      </v-row>

      <!-- 消息路由信息 -->
      <v-row class="mt-4">
        <v-col cols="12">
          <v-card variant="outlined">
            <v-card-title class="d-flex align-center">
              <v-icon color="info" class="mr-2">mdi-routes</v-icon>
              <span>消息路由规则</span>
            </v-card-title>
            <v-card-text>
              <div class="route-info">
                <div class="route-item">
                  <v-chip color="success" size="small" class="mr-2">双发</v-chip>
                  <span>ID 1100, 2048 → WebSocket + USB</span>
                </div>
                <div class="route-item mt-2">
                  <v-chip color="primary" size="small" class="mr-2">网口</v-chip>
                  <span>其他所有消息 → WebSocket</span>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 操作按钮 -->
      <v-row class="mt-4">
        <v-col cols="12">
          <div class="d-flex gap-2">
            <v-btn
              color="primary"
              variant="outlined"
              @click="openUsbConfig"
              prepend-icon="mdi-cog"
            >
              USB 配置
            </v-btn>
            <v-btn
              color="warning"
              variant="outlined"
              @click="restartService"
              :loading="restarting"
              prepend-icon="mdi-restart"
            >
              重启服务
            </v-btn>
            <v-btn
              color="info"
              variant="outlined"
              @click="checkHealth"
              :loading="checking"
              prepend-icon="mdi-heart-pulse"
            >
              健康检查
            </v-btn>
            <v-btn
              color="success"
              variant="outlined"
              @click="testMessage"
              :loading="testing"
              prepend-icon="mdi-send"
            >
              测试消息
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-card-text>

    <!-- USB配置对话框 -->
    <v-dialog v-model="usbConfigDialog" max-width="600">
      <v-card>
        <v-card-title>USB 连接配置</v-card-title>
        <v-card-text>
          <v-form ref="usbForm">
            <v-text-field
              v-model="usbConfig.devicePath"
              label="设备路径"
              placeholder="例如: /dev/ttyUSB0 或 COM3"
              prepend-icon="mdi-usb"
              clearable
            ></v-text-field>
            
            <v-switch
              v-model="usbConfig.enabled"
              label="启用 USB 连接"
              color="primary"
            ></v-switch>
            
            <v-switch
              v-model="usbConfig.autoDetect"
              label="自动检测 USB 设备"
              color="primary"
            ></v-switch>

            <v-btn
              color="info"
              variant="outlined"
              @click="listSerialPorts"
              :loading="listingPorts"
              prepend-icon="mdi-format-list-bulleted"
              class="mb-3"
            >
              列出可用端口
            </v-btn>

            <v-list v-if="availablePorts.length > 0" class="mt-3">
              <v-list-subheader>可用串口设备</v-list-subheader>
              <v-list-item
                v-for="port in availablePorts"
                :key="port.path"
                @click="selectPort(port.path)"
                class="port-item"
              >
                <template v-slot:prepend>
                  <v-icon>mdi-usb</v-icon>
                </template>
                <v-list-item-title>{{ port.path }}</v-list-item-title>
                <v-list-item-subtitle>
                  {{ port.manufacturer || '未知制造商' }}
                  <span v-if="port.vendorId">- VID: {{ port.vendorId }}</span>
                  <span v-if="port.productId">- PID: {{ port.productId }}</span>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="usbConfigDialog = false">取消</v-btn>
          <v-btn
            color="primary"
            @click="saveUsbConfig"
            :loading="savingConfig"
          >
            保存
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ipc } from '@/utils/ipcRenderer'

// 响应式数据
const loading = ref(false)
const restarting = ref(false)
const checking = ref(false)
const testing = ref(false)
const savingConfig = ref(false)
const listingPorts = ref(false)

const connectionStatus = ref({
  isStarted: false,
  websocket: {
    isConnected: false,
    isConnecting: false,
    url: '',
    reconnectAttempts: 0,
    lastMessageTime: 0
  },
  usb: {
    isConnected: false,
    isConnecting: false,
    portPath: null,
    reconnectAttempts: 0,
    lastMessageTime: 0
  }
})

const usbConfigDialog = ref(false)
const usbConfig = reactive({
  devicePath: '',
  enabled: true,
  autoDetect: true
})

const availablePorts = ref([])

// 计算属性
const serviceStatus = computed(() => ({
  isStarted: connectionStatus.value.isStarted
}))

const websocketConnection = computed(() => connectionStatus.value.websocket)
const usbConnection = computed(() => connectionStatus.value.usb)

const statusColor = computed(() => {
  if (serviceStatus.value.isStarted &&
      (websocketConnection.value.isConnected || usbConnection.value.isConnected)) {
    return 'success'
  }
  return 'warning'
})

const statusIcon = computed(() => {
  if (serviceStatus.value.isStarted &&
      (websocketConnection.value.isConnected || usbConnection.value.isConnected)) {
    return 'mdi-check-circle'
  }
  return 'mdi-alert-circle'
})

// 方法
const refreshStatus = async () => {
  loading.value = true
  try {
    const result = await ipc.invoke('controller/connection/getStatus')
    if (result.success) {
      connectionStatus.value = result.data
    }
  } catch (error) {
    console.error('Failed to refresh connection status:', error)
  } finally {
    loading.value = false
  }
}

const testMessage = async () => {
  testing.value = true
  try {
    // 测试发送ID1100消息（双发）
    const result = await ipc.invoke('controller/connection/sendMessage', {
      messageId: 1100,
      body: {
        vehicleSubType: 0,
        tabletStatus: 64,
        failureLevel: 255
      },
      target: 'auto'
    })

    if (result.success) {
      await ipc.invoke('controller/message/sendNotification', {
        type: 'success',
        content: `测试消息发送成功 (${result.data.target})`,
        mode: 'snackbar',
        duration: 3000
      })
    } else {
      await ipc.invoke('controller/message/sendNotification', {
        type: 'error',
        content: `测试消息发送失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to send test message:', error)
    await ipc.invoke('controller/message/sendNotification', {
      type: 'error',
      content: `测试消息发送失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    testing.value = false
  }
}

const restartService = async () => {
  restarting.value = true
  try {
    const result = await ipc.invoke('controller/connection/restartService')
    if (result.success) {
      await refreshStatus()
      await ipc.invoke('controller/message/sendNotification', {
        type: 'success',
        content: '通信服务重启成功',
        mode: 'snackbar',
        duration: 3000
      })
    } else {
      await ipc.invoke('controller/message/sendNotification', {
        type: 'error',
        content: `服务重启失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to restart service:', error)
    await ipc.invoke('controller/message/sendNotification', {
      type: 'error',
      content: `服务重启失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    restarting.value = false
  }
}

const checkHealth = async () => {
  checking.value = true
  try {
    const result = await ipc.invoke('controller/connection/checkHealth')
    if (result.success) {
      const { isHealthy } = result.data
      await ipc.invoke('controller/message/sendNotification', {
        type: isHealthy ? 'success' : 'warning',
        content: `健康检查结果: ${isHealthy ? '正常' : '异常'}`,
        mode: 'snackbar',
        duration: 3000
      })
      await refreshStatus()
    }
  } catch (error) {
    console.error('Failed to check health:', error)
    await ipc.invoke('controller/message/sendNotification', {
      type: 'error',
      content: `健康检查失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    checking.value = false
  }
}

const stopWebsocket = async () => {
 try {
   const result = await ipc.invoke('controller/connection/stopWebsocketConnection');
   if (result.success) {
     await refreshStatus();
     await ipc.invoke('controller/message/sendNotification', {
       type: 'success',
       content: 'WebSocket连接已停止',
       mode: 'snackbar',
       duration: 3000
     });
   } else {
     await ipc.invoke('controller/message/sendNotification', {
       type: 'error',
       content: `停止WebSocket连接失败: ${result.error}`,
       mode: 'snackbar',
       duration: 5000
     });
   }
 } catch (error) {
   console.error('Failed to stop WebSocket connection:', error);
   await ipc.invoke('controller/message/sendNotification', {
     type: 'error',
     content: `停止WebSocket连接失败: ${error.message}`,
     mode: 'snackbar',
     duration: 5000
   });
 }
};

const stopUsb = async () => {
 try {
   const result = await ipc.invoke('controller/connection/stopUsbConnection');
   if (result.success) {
     await refreshStatus();
     await ipc.invoke('controller/message/sendNotification', {
       type: 'success',
       content: 'USB连接已停止',
       mode: 'snackbar',
       duration: 3000
     });
   } else {
     await ipc.invoke('controller/message/sendNotification', {
       type: 'error',
       content: `停止USB连接失败: ${result.error}`,
       mode: 'snackbar',
       duration: 5000
     });
   }
 } catch (error) {
   console.error('Failed to stop USB connection:', error);
   await ipc.invoke('controller/message/sendNotification', {
     type: 'error',
     content: `停止USB连接失败: ${error.message}`,
     mode: 'snackbar',
     duration: 5000
   });
 }
};
const openUsbConfig = async () => {
  try {
    const result = await ipc.invoke('controller/connection/getUsbConfig')
    if (result.success) {
      Object.assign(usbConfig, result.data)
    }
    usbConfigDialog.value = true
  } catch (error) {
    console.error('Failed to get USB config:', error)
  }
}

const saveUsbConfig = async () => {
  savingConfig.value = true
  try {
    const result = await ipc.invoke('controller/connection/updateUsbConfig', usbConfig)
    if (result.success) {
      usbConfigDialog.value = false
      await ipc.invoke('controller/message/sendNotification', {
        type: 'success',
        content: 'USB 配置保存成功',
        mode: 'snackbar',
        duration: 3000
      })
    } else {
      await ipc.invoke('controller/message/sendNotification', {
        type: 'error',
        content: `配置保存失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to save USB config:', error)
    await ipc.invoke('controller/message/sendNotification', {
      type: 'error',
      content: `配置保存失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    savingConfig.value = false
  }
}

const listSerialPorts = async () => {
  listingPorts.value = true
  try {
    const result = await ipc.invoke('controller/connection/listSerialPorts')
    if (result.success) {
      availablePorts.value = result.data
    } else {
      await ipc.invoke('controller/message/sendNotification', {
        type: 'error',
        content: `获取串口列表失败: ${result.error}`,
        mode: 'snackbar',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Failed to list serial ports:', error)
    await ipc.invoke('controller/message/sendNotification', {
      type: 'error',
      content: `获取串口列表失败: ${error.message}`,
      mode: 'snackbar',
      duration: 5000
    })
  } finally {
    listingPorts.value = false
  }
}

const selectPort = (path) => {
  usbConfig.devicePath = path
}

// 工具方法
const getStateText = (isConnected) => {
  return isConnected ? '已连接' : '已断开'
}

const getStateColor = (isConnected) => {
  return isConnected ? 'success' : 'error'
}

const getStateClass = (isConnected) => {
  return isConnected ? 'state-connected' : 'state-disconnected'
}

const formatTime = (timestamp) => {
  if (!timestamp) return '无'
  const date = new Date(timestamp)
  return date.toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  refreshStatus()

  // 监听双连接状态变化
  ipc.on('connection-status-changed', (data) => {
    if (data.connectionStatus) {
      connectionStatus.value = data.connectionStatus
    }
  })

  // 监听消息接收
  ipc.on('ipc-message', (data) => {
    console.log(`收到来自 ${data.connectionType} 的消息:`, data.id)
  })

  // 定期刷新状态
  const interval = setInterval(refreshStatus, 30000) // 每30秒刷新一次

  onUnmounted(() => {
    clearInterval(interval)
    ipc.removeAllListeners('connection-status-changed')
    ipc.removeAllListeners('ipc-message')
  })
})
</script>

<style scoped lang="scss">
.connection-monitor {
  .connection-card {
    height: 100%;
  }

  .connection-info {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .label {
        font-weight: 500;
        color: rgb(var(--v-theme-on-surface-variant));
      }
    }
  }

  .state-connected {
    color: rgb(var(--v-theme-success));
    font-weight: 500;
  }

  .state-connecting {
    color: rgb(var(--v-theme-warning));
    font-weight: 500;
  }

  .state-disconnected {
    color: rgb(var(--v-theme-error));
    font-weight: 500;
  }

  .state-error {
    color: rgb(var(--v-theme-error));
    font-weight: 500;
  }

  .port-item {
    cursor: pointer;

    &:hover {
      background-color: rgba(var(--v-theme-primary), 0.1);
    }
  }

  .route-info {
    .route-item {
      display: flex;
      align-items: center;
      padding: 8px 0;

      span {
        color: rgb(var(--v-theme-on-surface));
      }
    }
  }
}
</style>
