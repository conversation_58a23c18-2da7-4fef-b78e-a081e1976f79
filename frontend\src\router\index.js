import { createRouter, createWeb<PERSON>ashHistory } from "vue-router";

const routes = [
  {
    path: "/",
    name: "home",
    redirect: "/switch",
    component: () => import("@/views/home/<USER>"),
    children: [
      {
        path: "/switch",
        name: "switch",
        component: () => import("@/views/switch/index.vue"),
      },
      {
        path: "/run",
        name: "run",
        component: () => import("@/views/run-stat/index.vue"),
      },
      {
        path: "/settings",
        name: "settings",
        component: () => import("@/views/settings/index.vue"),
      },
      {
        path: "/more-func",
        name: "more-func",
        component: () => import("@/views/more-func/index.vue"),
      },
      {
        path: "/general",
        name: "general",
        component: () => import("@/views/settings/general.vue"),
      },
      {
        path: "/sensitivity",
        name: "sensitivity",
        component: () => import("@/views/sensitivity/index.vue"),
      },
      {
        path: "/intelligence",
        name: "intelligence",
        component: () => import("@/views/intelligence/index.vue"),
      },
      {
        path: "/auxiliary",
        name: "auxiliary",
        component: () => import("@/views/auxiliary/index.vue"),
      },
      {
        path: "/network",
        name: "network",
        component: () => import("@/views/network/index.vue"),
      },
      {
        path: "/normal",
        name: "normal",
        component: () => import("@/views/normal/index.vue"),
      },
      {
        path: "/widget",
        name: "widget",
        component: () => import("@/views/widget/index.vue"),
      },
      {
        path: "/video",
        name: "video",
        component: () => import("@/views/video/index.vue"),
      },
    ],
  },
  {
    path: "/debug",
    name: "debug",
    component: () => import("@/views/debug/index.vue"),
  },
  {
    path: "/debug/mqtt",
    name: "debug-mqtt",
    component: () => import("@/views/debug/mqtt/index.vue"),
  },
  {
    path: "/debug/params",
    name: "debug-params",
    component: () => import("@/views/debug/params/index.vue"),
  },
  {
    path: "/debug/play",
    name: "debug-play",
    component: () => import("@/views/debug/play/index.vue"),
  },
  {
    path: "/debug/log",
    name: "debug-log",
    component: () => import("@/views/debug/log/index.vue"),
  },
  {
    path: "/debug/network",
    name: "debug-network",
    component: () => import("@/views/debug/network/index.vue"),
  },
  {
    path: "/debug/screen",
    name: "debug-screen",
    component: () => import("@/views/debug/screen-info/index.vue"),
  },
  {
    path: "/debug/button",
    name: "debug-button",
    component: () => import("@/views/debug/button/index.vue"),
  },
  {
    path: "/debug/ctrl",
    name: "debug-ctrl",
    component: () => import("@/views/debug/ctrl/index.vue"),
  },
  {
    path: "/debug/memory",
    name: "debug-memory",
    component: () => import("@/views/debug/memory/index.vue"),
  },
  {
    path: "/debug/storage",
    name: "debug-storage",
    component: () => import("@/views/debug/storage/index.vue"),
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("@/views/home/<USER>"),
  },
  {
    path: "/show",
    name: "show",
    component: () => import("@/views/home/<USER>"),
  },
  {
    path: "/hello",
    name: "hello",
    component: () => import("@/views/hello/index.vue"),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
