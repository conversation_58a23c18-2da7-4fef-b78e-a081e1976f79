<template>
  <!-- <div ref="playerContainer" id="mse" class="w-[640px] h-[360px] bg-slate-400"></div> -->
  <div class="debug-player">
    <video ref="videoElement" controls autoplay muted name="media" class="video-element">
      <source src="https://oms.apps.builderx.com/api/v2/data_manage/media/video/play/68bfc10722623348756bd543.mp4" type="video/mp4" />
      您的浏览器不支持 video 标签。
    </video>
    <p class="info">
      如果视频无法播放，请检查：<br />
      1. 视频文件 <code>stream0.mp4</code> 是否已放置在 <code>frontend/public/videos/</code> 目录中。<br />
      2. 浏览器控制台是否有错误信息。
    </p>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
// import Player from "xgplayer";
// import "xgplayer/dist/index.min.css";

const player = ref(null);
const playerContainer = ref(null);

const videoElement = ref(null);

onMounted(() => {

  // player.value = new Player({
  //   id: "mse", // 挂载点
  //   // url: "https://oms.apps.builderx.com/api/v2/data_manage/media/video/play/68be4b8722623348756bd4bb.mp4",
  //   url:"//sf1-cdn-tos.huoshanstatic.com/obj/media-fe/xgplayer_doc_video/mp4/xgplayer-demo-360p.mp4",
  //   autoplay: false,
  //   fluid: true, // 自适应容器宽度
  //   controls: true, // 显示控制条
  // });

  if (videoElement.value) {
    const v = videoElement.value;
    v.onerror = (e) => {
      console.error("Video error:", e, v.error);
    };
    v.onloadeddata = () => {
      console.log("Video loaded successfully");
    };
  }

});
</script>

<style scoped>
.debug-player {
  padding: 20px;
  border: 1px solid #ccc;
  margin-top: 20px;
  background-color: #f9f9f9;
}
.video-element {
  width: 100%;
  max-width: 600px;
  margin-top: 10px;
}
.info {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}
</style>
