<template>
  <div>
    <v-main style="--v-layout-top: 0px; --v-layout-bottom: 0px">
      <router-view></router-view>
    </v-main>
    <v-app-bar :elevation="1" class="px-4 h-[--app-bar-height] justify-center bg-black" location="bottom">
      <div class="d-flex w-full h-full items-center">
        <div class="d-flex flex-1 items-center justify-center gap-2">
          <v-btn
            v-for="item in navItems"
            :key="item.path"
            variant="text"
            :class="{ 'v-btn--active': isNavItemActive(item) }"
            @click="handleNavigation(item.path)"
            class="nav-btn"
          >
            <v-icon start>{{ item.icon }}</v-icon>
            {{ item.title }}
          </v-btn>
        </div>
        <!-- <v-btn
            @click="toggleTheme"
            color="primary"
            icon="mdi-weather-night"
            :icon="theme.global.current.value.dark ? 'mdi-weather-sunny' : 'mdi-weather-night'"
            variant="text"
            class="theme-btn"
          /> -->
        <div class="d-flex align-center gap-4">
          <v-btn @click="router.push('/debug')" color="primary" variant="outlined" class="back-btn">
            <v-icon start>mdi-arrow-left</v-icon>
            进后台
          </v-btn>
        </div>
      </div>
    </v-app-bar>
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { useTheme } from "vuetify";
import { ref, computed } from "vue";

const { t, locale } = useI18n();
const router = useRouter();
const route = useRoute();
const theme = useTheme();

const navItems = ref([
  // { path: "/intelligence", icon: "mdi-brain", title: "智能化功能" },
  // { path: "/sensitivity", icon: "mdi-gesture-tap", title: "操作灵敏" },
  // { path: "/settings", icon: "mdi-cog", title: "通用设置" },
  // { path: "/network", icon: "mdi-lan", title: "网络状态" },
  // { path: "/normal", icon: "mdi-run", title: "常态化运行" },
  // { path: "/run", icon: "mdi-run", title: "运行状态" },
  { path: "/switch", icon: "mdi-lan", title: "常用按钮" },
  { path: "/normal", icon: "mdi-refresh", title: "效率统计" },
  { path: "/auxiliary", icon: "mdi-monitor-dashboard", title: "辅助画面" },
  { path: "/widget", icon: "mdi-run", title: "组件布局" },
  { path: "/video", icon: "mdi-run", title: "视频存储" },
  { path: "/network", icon: "mdi-wifi", title: "网络状态" },
  { path: "/more-func", icon: "mdi-cog", title: "更多功能" },
]);

const moreFuncRoutes = ["/intelligence", "/sensitivity", "/general"];

const isNavItemActive = (item) => {
  if (item.path === "/more-func") {
    return route.path === item.path || moreFuncRoutes.includes(route.path);
  }
  return route.path === item.path;
};

const handleNavigation = (path) => {
  router.push(path);
};

const toggleTheme = () => {
  theme.global.name.value = theme.global.current.value.dark ? "light" : "dark";
};
</script>

<style scoped>
.nav-btn {
  font-weight: 500;
  min-width: 156px;
  height: 54px;
  font-size: 20px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  padding: 0 16px;
}

.nav-btn:hover {
  background-color: rgb(var(--v-theme-primary), 0.2);
}

.v-btn--active {
  background-color: rgb(var(--v-theme-primary), 0.2);
  color: rgb(var(--v-theme-primary)) !important;
}

.theme-btn {
  border-radius: 50%;
  transition: transform 0.3s ease;
  width: 80px;
  height: 80px;
  font-size: 24px;
  padding: 8px;
}

.theme-btn:hover {
  transform: rotate(30deg);
}

.back-btn {
  font-weight: 500;
  min-width: 90px;
  height: 40px;
  letter-spacing: 0.5px;
  padding: 0 16px;
}
</style>
