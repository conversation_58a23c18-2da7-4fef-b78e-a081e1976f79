<template>
  <div>
    <v-card>
      <v-card-title class="d-flex align-center">
        实时画面
        <v-spacer></v-spacer>
        <v-btn-group>
          <v-btn density="compact" @click="startStream" :disabled="isPlaying">开始播放</v-btn>
          <v-btn density="compact" @click="stopStream" :disabled="!isPlaying">停止播放</v-btn>
        </v-btn-group>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <div class="video-container" ref="videoContainer">
          <canvas ref="videoCanvas" width="640" height="360"></canvas>
          <div v-if="!isPlaying" class="video-overlay">
            <v-icon size="40" color="white" @click="startStream">mdi-play-circle-outline</v-icon>
            <div>点击开始播放按钮开始播放</div>
          </div>
          <div v-if="isLoading" class="video-overlay">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <div>正在加载视频流...</div>
          </div>
          <div v-if="errorMessage" class="video-overlay error">
            <v-icon size="large" color="error">mdi-alert-circle-outline</v-icon>
            <div>{{ errorMessage }}</div>
          </div>
        </div>
      </v-card-text>
    </v-card>
    <BackButton />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import BackButton from "@/components/BackButton/index.vue";
import { ipc } from "@/utils/ipcRenderer";
import JSMpeg from "@cycjimmy/jsmpeg-player";

const videoCanvas = ref(null);
const videoContainer = ref(null);
const isPlaying = ref(false);
const isLoading = ref(false);
const errorMessage = ref("");
let player = null;

// 开始播放流
const startStream = async () => {
  try {
    isLoading.value = true;
    errorMessage.value = "";

    // 通过IPC调用后端服务启动流
    await ipc.invoke("controller/example/startStream");

    // 等待一段时间确保服务启动
    setTimeout(() => {
      connectWebSocket();
    }, 3000);
  } catch (error) {
    console.error("启动流失败:", error);
    errorMessage.value = `启动流失败: ${error.message || "未知错误"}`;
    isLoading.value = false;
  }
};

// 停止播放流
const stopStream = async () => {
  try {
    // 关闭播放器
    if (player) {
      player.destroy();
      player = null;
    }

    // 通过IPC调用后端服务停止流
    await ipc.invoke("controller/example/stopStream");

    isPlaying.value = false;
    isLoading.value = false;
    errorMessage.value = "";
  } catch (error) {
    console.error("停止流失败:", error);
    errorMessage.value = `停止流失败: ${error.message || "未知错误"}`;
  }
};

// 连接WebSocket
const connectWebSocket = async () => {
  try {
    // 创建JSMpeg播放器
    const url = await ipc.invoke("controller/message/getLocalIP");

    console.log(url);

    player = new JSMpeg.Player(`ws://${url}:8083`, {
      canvas: videoCanvas.value,
      autoplay: true,
      protocols: [],
      streamType: "mse",
      chunkSize: 1024 * 512,
      onSourceEstablished: () => {
        console.log("视频源已建立连接");
        isPlaying.value = true;
        isLoading.value = false;
      },
      onError: (error) => {
        console.error("播放器错误:", error);
        errorMessage.value = `播放器错误: ${error || "连接失败"}`;
        isLoading.value = false;
        stopStream();
      },
    });
  } catch (error) {
    console.error("创建播放器失败:", error);
    errorMessage.value = `创建播放器失败: ${error.message || "未知错误"}`;
    isLoading.value = false;
  }
};

// 组件卸载时清理资源
onUnmounted(() => {
  stopStream();
});
</script>

<style scoped>
.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  margin: 0 auto;
}

canvas {
  width: 100%;
  height: 100%;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  z-index: 10;
}

.video-overlay.error {
  background-color: rgba(0, 0, 0, 0.8);
  color: #ff5252;
}

.video-overlay div {
  text-align: center;
  padding: 0 20px;
}
</style>
