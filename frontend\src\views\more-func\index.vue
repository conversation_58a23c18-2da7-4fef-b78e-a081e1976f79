<template>
  <div class="switch-wrap">
    <div class="debug-area left" @click="handleLeftClick"></div>
    <div class="debug-area right" @click="handleRightClick"></div>
    <div class="switch-container" :class="gridClass">
      <div
        v-for="item in switches"
        :key="item.id"
        class="switch-item"
        :class="{ disabled: item.disabled }"
        @click="handleClick(item)"
      >
        <v-icon size="x-large" class="switch-icon">{{ item.icon }}</v-icon>
        <div class="switch-label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
// 示例数据，实际使用时可以通过props传入
const switches = ref([
  {
    id: 4,
    label: "智能化功能",
    icon: "mdi-lightbulb",
    path: "/intelligence",
  },
  {
    id: 2,
    label: "操作灵敏度",
    icon: "mdi-steering",
    path: "/sensitivity",
  },
  {
    id: 1,
    label: "常规设置",
    icon: "mdi-cog",
    path: "/general",
  },
  // {
  //   id: 3,
  //   label: "辅助画面",
  //   icon: "mdi-monitor-dashboard",
  //   path: "/auxiliary",
  // },
]);

// 根据开关数量计算网格布局类名
const gridClass = computed(() => {
  const count = switches.value.length;
  switch (count) {
    case 1:
      return "grid-one";
    case 2:
      return "grid-two";
    case 3:
      return "grid-three";
    case 4:
      return "grid-four";
    case 5:
      return "grid-five";
    case 6:
      return "grid-six";
    default:
      return "grid-one";
  }
});

const handleClick = (item) => {
  const { path } = item;
  if (path) {
    router.push(path);
  }
};

const leftClickTimes = ref(0);
const rightClickTimes = ref(0);
const lastClickTime = ref(0);

const resetClicks = () => {
  leftClickTimes.value = 0;
  rightClickTimes.value = 0;
};

const handleLeftClick = () => {
  const now = Date.now();
  if (now - lastClickTime.value > 3000) {
    resetClicks();
  }
  lastClickTime.value = now;
  leftClickTimes.value++;
  checkDebugSequence();
};

const handleRightClick = () => {
  const now = Date.now();
  if (now - lastClickTime.value > 3000) {
    resetClicks();
  }
  lastClickTime.value = now;
  rightClickTimes.value++;
  checkDebugSequence();
};

const checkDebugSequence = () => {
  if (leftClickTimes.value === 2 && rightClickTimes.value === 2) {
    resetClicks();
    router.push("/debug");
  }
};

</script>

<style scoped>
.switch-wrap {
  height: calc(100vh - var(--app-bar-height));
  width: 100%;
}
.switch-container {
  height: calc(100vh - var(--app-bar-height));
  padding: 60px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: grid;
  gap: 32px;
  width: 80%;
  max-width: 1200px;
  margin: 0 auto;
}

.switch-icon {
  font-size: 56px;
  margin-bottom: 16px;
}

.switch-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background-color: rgb(var(--v-theme-surface));
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  min-height: 280px;
  min-width: 350px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.switch-item:hover {
  transform: scale(1.02);
  background-color: rgb(var(--v-theme-primary), 0.7);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  color: white;
}

.switch-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.switch-label {
  font-size: 30px;
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
}

/* 一个开关时居中 */
.grid-one {
  grid-template-columns: 1fr;
  justify-items: center;
  align-items: center;
  & .switch-item {
    width: 500px;
  }
}

/* 两个开关时左右排列 */
.grid-two {
  grid-template-columns: 1fr 1fr;
}

/* 三个开关时左中右排列 */
.grid-three {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 四个开关时2x2排列 */
.grid-four {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 五个开关时上面三个下面两个 */
.grid-five {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 六个开关时3x2排列 */
.grid-six {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.debug-area {
  position: fixed;
  width: 120px;
  height: 120px;
  z-index: 9999;
}

.debug-area.left {
  top: 0;
  left: 0;
}

.debug-area.right {
  top: 0;
  right: 0;
}
</style>
