<template>
  <div class="video-page">

    <div class="controls">
      <!-- <v-menu
        ref="menu"
        v-model="menu"
        :close-on-content-click="false"
        transition="scale-transition"
        offset-y
        min-width="auto"
      >
        <template v-slot:activator="{ on, attrs }">
          <v-text-field
            v-model="dateFormatted"
            label="选择日期"
            prepend-icon="mdi-calendar"
            readonly
            v-bind="attrs"
            v-on="on"
          ></v-text-field>
        </template>
        <v-date-picker
          v-model="selectedDate"
          no-title
          scrollable
          @input="menu = false; handleDateChange()"
          ></v-date-picker>
        </v-menu> -->
    </div>

    <debug-player />

    <div v-if="loading" class="loading-tip">正在加载视频数据...</div>
    <div v-else-if="error" class="error-tip">
      {{ error }} <v-btn @click="fetchVideos" text color="primary">点击重试</v-btn>
    </div>
    <!-- <div v-else-if="videoSegments.length === 0" class="empty-tip">当天没有视频片段</div>
    <template v-else>
      <video-timeline :segments="videoSegments" :selected-date="selectedDate" @time-selected="handleTimeSelected" />
      <video-player
        ref="videoPlayer"
        :src="currentVideoSrc"
        :segment-start-time-utc="currentSegment?.segment_start_time"
        :segment-id="currentSegment?.id"
      />
    </template> -->
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from "vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import VideoTimeline from "./components/VideoTimeline.vue";
import VideoPlayer from "./components/VideoPlayer.vue";
import DebugPlayer from "./DebugPlayer.vue"; // 引入调试组件
import { getVideoList } from "@/api/omsApi"; // 假设API在index.js中
import { ipc } from "@/utils/ipcRenderer";

dayjs.extend(utc);

const SERVER_BASE = "http://***************:8080"; // 应该从配置中读取

const selectedDate = ref(dayjs().format("YYYY-MM-DD"));
const menu = ref(false);
const videoSegments = ref([]);
const loading = ref(false);
const error = ref(null);
const pageData = ref({ vehicle_id: null });
const currentSegment = ref(null);
const videoPlayer = ref(null);

const dateFormatted = computed(() => {
  return dayjs(selectedDate.value).format("YYYY-MM-DD");
});

const currentVideoSrc = computed(() => {
  if (currentSegment.value) {
    return `${SERVER_BASE}/api/v2/data_manage/media/video/play/${currentSegment.value.id}.mp4`;
  }
  return "";
});

async function fetchVehicleId() {
  try {
    // 在Electron环境中，ipc是可用的
    pageData.value.vehicle_id = await ipc.invoke("global-state:get", "vehicle_id");
  } catch (e) {
    console.error("获取 vehicle_id 失败:", e);
    error.value = "获取车辆ID失败";
  }
}

async function fetchVideos() {
  if (!pageData.value.vehicle_id) {
    error.value = "缺少 vehicle_id";
    return;
  }

  loading.value = true;
  error.value = null;
  videoSegments.value = [];

  const startOfDay = dayjs(selectedDate.value).startOf("day");
  const endOfDay = dayjs(selectedDate.value).endOf("day");

  const params = {
    page_no: 1,
    page_size: 100, // 暂时获取足够多的数据，后续实现分页
    start_time: startOfDay.utc().toISOString(),
    end_time: endOfDay.utc().toISOString(),
    vehicle_id: pageData.value.vehicle_id,
  };

  try {
    // const response = await getVideoList(params); // 调用API
    // const lists = response.data?.lists || [];
    const lists =  [];

    // 计算每个片段的时长
    lists.sort((a, b) => new Date(a.segment_start_time) - new Date(b.segment_start_time));

    const segmentsWithDuration = lists.map((segment, index) => {
      let duration;
      if (index < lists.length - 1) {
        const nextSegmentStartTime = dayjs(lists[index + 1].segment_start_time);
        duration = nextSegmentStartTime.diff(dayjs(segment.segment_start_time), "second");
      } else {
        duration = 300; // Fallback duration in seconds (5 minutes)
      }
      return { ...segment, duration };
    });

    videoSegments.value = segmentsWithDuration;
    if (videoSegments.value.length > 0) {
      currentSegment.value = videoSegments.value[0];
    }
  } catch (e) {
    console.error("获取视频列表失败:", e);
    error.value = "获取视频列表失败";
  } finally {
    loading.value = false;
  }
}

function handleDateChange() {
  fetchVideos();
}

function handleTimeSelected({ segment, offsetSeconds }) {
  currentSegment.value = segment;
  // 使用 nextTick 确保 videoPlayer 组件已经更新了 src
  nextTick(() => {
    if (videoPlayer.value) {
      videoPlayer.value.seekTo(offsetSeconds);
    }
  });
}

onMounted(async () => {
  await fetchVehicleId();
  if (pageData.value.vehicle_id) {
    fetchVideos();
  }
});
</script>

<style scoped>
.video-page {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}
.controls {
  margin-bottom: 20px;
}
.loading-tip,
.error-tip,
.empty-tip {
  text-align: center;
  margin-top: 50px;
  color: #888;
}
</style>
