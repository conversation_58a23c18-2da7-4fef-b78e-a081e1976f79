import { get, post, put, del } from "./index";

// const token = "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a"; // 本地
const token = "Bearer t-05393aef3c9871ec3eb5759fbde7c329d668d05433eb"; // oms

export const getLayoutList = (vehicleID) => {
  return get(
    `/vehicles/${vehicleID}/service/mix_template`,
    {},
    { headers: { authorization: token } }
  );
};

export const setLayout = (vehicleID, layout) => {
  return put(`/vehicles/${vehicleID}/service/mix_layout`, layout, {
    headers: { authorization: token },
  });
};

// 事件上报
export const reportEventApi = (event) => {
  return post(`prod_count/events`, event, {
    headers: { authorization: token },
  });
};

// 获取车辆配置
export const getVehicleConfigApi = (vehicleID) => {
  return get(
    `/prod_count/config/${vehicleID}`,
    {},
    {
      headers: { authorization: token },
    }
  );
};

// 配置车辆
export const setVehicleConfigApi = (vehicleID, config) => {
  return put(`/prod_count/config/${vehicleID}`, config, {
    headers: { authorization: token },
  });
};

// 历史装车记录
export const getHistoryLoadApi = (vehicleID) => {
  return get(
    `/prod_count/history_load`,
    { vehicle_id: vehicleID, page_size: 50, page_no: 1 },
    {
      headers: { authorization: token },
    }
  );
};

// 最近班组记录
export const getRecentTeamApi = (vehicleID) => {
  return get(
    `/prod_count/recent_day_team/${vehicleID}`,
    {},
    {
      headers: { authorization: token },
    }
  );
};

// 历史班组记录
export const getHistoryTeamApi = (vehicleID) => {
  return get(
    `/prod_count/history_team`,
    { vehicle_id: vehicleID, page_size: 50, page_no: 1 },
    {
      headers: { authorization: token },
    }
  );
};

// 排行榜查询
// 历史装车最快班次
// 历史装车最多次数


/**
 * 获取视频列表
 * @param {Object} params - 查询参数
 * @param {number} params.page_no - 页码
 * @param {number} params.page_size - 每页数量
 * @param {string} params.start_time - 开始时间 (UTC ISO string)
 * @param {string} params.end_time - 结束时间 (UTC ISO string)
 * @param {string} params.vehicle_id - 车辆ID
 * @returns {Promise}
 */
export const getVideoList = (params) => {
  return get('/data_manage/media/video', params, {
    headers: { authorization: token },
  });
};