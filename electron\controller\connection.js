"use strict";

const { logger } = require("ee-core/log");
const DualConnectionService = require("../service/dualConnectionService");
const globalStateManager = require("../service/globalStateManager");
const MessageEncoder = require("../utils/messageEncoder");

/**
 * 连接管理控制器
 * 提供连接状态查询、切换等功能的前端接口
 */
class ConnectionController {
  /**
   * 获取连接状态
   */
  async getStatus(args, event) {
    try {
      const status = DualConnectionService.getConnectionStatus();
      logger.debug("[ConnectionController] Connection status retrieved");
      return {
        success: true,
        data: status
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to get connection status:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取详细连接信息
   */
  async getDetailedInfo(args, event) {
    try {
      const info = DualConnectionService.getConnectionStatus();
      logger.debug("[ConnectionController] Detailed connection info retrieved");
      return {
        success: true,
        data: info
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to get detailed connection info:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查服务健康状态
   */
  async checkHealth(args, event) {
    try {
      const isHealthy = await DualConnectionService.isHealthy();
      logger.debug(`[ConnectionController] Health check result: ${isHealthy}`);

      return {
        success: true,
        data: {
          isHealthy,
          timestamp: Date.now(),
          status: DualConnectionService.getConnectionStatus()
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Health check failed:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取USB配置
   */
  async getUsbConfig(args, event) {
    try {
      const usbConfig = await globalStateManager.get("usb");
      logger.debug("[ConnectionController] USB config retrieved");
      
      return {
        success: true,
        data: usbConfig || {
          devicePath: "",
          enabled: true,
          autoDetect: true
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to get USB config:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 更新USB配置
   * @param {Object} args - 参数
   * @param {string} args.devicePath - USB设备路径
   * @param {boolean} args.enabled - 是否启用USB连接
   * @param {boolean} args.autoDetect - 是否自动检测USB设备
   */
  async updateUsbConfig(args, event) {
    const { devicePath, enabled, autoDetect } = args;
    
    try {
      const currentConfig = await globalStateManager.get("usb") || {};
      
      const newConfig = {
        ...currentConfig,
        ...(devicePath !== undefined && { devicePath }),
        ...(enabled !== undefined && { enabled }),
        ...(autoDetect !== undefined && { autoDetect })
      };
      
      await globalStateManager.set("usb", newConfig);
      logger.info("[ConnectionController] USB config updated:", newConfig);
      
      return {
        success: true,
        data: newConfig
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to update USB config:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 列出可用的串口设备
   */
  async listSerialPorts(args, event) {
    try {
      const { SerialPort } = require("serialport");
      const ports = await SerialPort.list();
      
      logger.debug(`[ConnectionController] Found ${ports.length} serial ports`);
      
      return {
        success: true,
        data: ports.map(port => ({
          path: port.path,
          manufacturer: port.manufacturer,
          serialNumber: port.serialNumber,
          vendorId: port.vendorId,
          productId: port.productId
        }))
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to list serial ports:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 测试USB连接
   * @param {Object} args - 参数
   * @param {string} args.devicePath - 要测试的设备路径
   */
  async testUsbConnection(args, event) {
    const { devicePath } = args;
    
    if (!devicePath) {
      return {
        success: false,
        error: "Device path is required"
      };
    }
    
    try {
      const { SerialPort } = require("serialport");
      
      // 尝试打开串口进行测试
      const testPort = new SerialPort({
        path: devicePath,
        baudRate: 115200,
        autoOpen: false
      });
      
      const testResult = await new Promise((resolve, reject) => {
        testPort.open((error) => {
          if (error) {
            reject(error);
          } else {
            testPort.close(() => {
              resolve(true);
            });
          }
        });
      });
      
      logger.info(`[ConnectionController] USB connection test successful: ${devicePath}`);
      
      return {
        success: true,
        data: {
          devicePath,
          testResult: true,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      logger.error(`[ConnectionController] USB connection test failed for ${devicePath}:`, error);
      return {
        success: false,
        error: error.message,
        data: {
          devicePath,
          testResult: false,
          timestamp: Date.now()
        }
      };
    }
  }

  /**
   * 重启通信服务
   */
  async restartService(args, event) {
    try {
      logger.info("[ConnectionController] Restarting dual connection service");

      // 停止服务
      await DualConnectionService.stop();

      // 等待一秒
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 重新启动服务
      await DualConnectionService.start();

      logger.info("[ConnectionController] Dual connection service restarted successfully");

      return {
        success: true,
        data: {
          restarted: true,
          timestamp: Date.now(),
          status: DualConnectionService.getConnectionStatus()
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to restart service:", error);
      return {
        success: false,
        error: error.message
      };
    }
}
  /**
   * 停止WebSocket连接
   */
  async stopWebsocketConnection(args, event) {
    try {
      logger.info("[ConnectionController] Stopping WebSocket connection");
      await DualConnectionService.stopWebsocket();
      logger.info("[ConnectionController] WebSocket connection stopped successfully");
      return {
        success: true,
        data: {
          stopped: true,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to stop WebSocket connection:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 停止USB连接
   */
  async stopUsbConnection(args, event) {
    try {
      logger.info("[ConnectionController] Stopping USB connection");
      await DualConnectionService.stopUsb();
      logger.info("[ConnectionController] USB connection stopped successfully");
      return {
        success: true,
        data: {
          stopped: true,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      logger.error("[ConnectionController] Failed to stop USB connection:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  /**
   * 发送消息
   * @param {Object} args - 参数对象
   * @param {number} args.messageId - 消息ID
   * @param {Object} args.body - 消息体
   * @param {string} args.target - 目标连接 ('auto', 'websocket', 'usb', 'all')
   */
  async sendMessage(args, event) {
    try {
      const { messageId, body, target = 'auto' } = args;

      if (!messageId || !body) {
        throw new Error("messageId and body are required");
      }

      let result;
      let actualTarget;

      if (target === 'auto') {
        // 自动路由：1100和2048走双通道，其余只走网口
        result = await DualConnectionService.sendMessageWithId(messageId, body);
        actualTarget = DualConnectionService.getMessageTarget(messageId);
      } else {
        // 指定目标发送
        const msgBody = await MessageEncoder.buildBody(messageId, body);
        const head = {
          vehicleType: 0,
          dataUnitLength: msgBody.length,
          transmitCount: MessageEncoder.getTransmitCount(messageId),
        };
        const msgHead = MessageEncoder.buildHead(head);
        const mergedBuffer = Buffer.concat([msgHead, msgBody]);
        const usbBuffer = MessageEncoder.packUsbMessage(mergedBuffer);

        result = await DualConnectionService.sendMessage(usbBuffer, target);
        actualTarget = target;
      }

      logger.info(`[ConnectionController] Message ${messageId} sent successfully`);
      return {
        success: true,
        data: {
          messageId,
          target: actualTarget,
          results: result
        }
      };

    } catch (error) {
      logger.error("[ConnectionController] Failed to send message:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = ConnectionController;
