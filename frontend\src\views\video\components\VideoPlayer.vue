<template>
  <div class="video-player">
    <video
      ref="videoElement"
      :src="src"
      controls
      autoplay
      @timeupdate="handleTimeUpdate"
      @loadedmetadata="handleLoadedMetadata"
      class="video-element"
    ></video>
    <div class="video-info">
      <p v-if="segmentId"><strong>片段ID:</strong> {{ segmentId }}</p>
      <p v-if="segmentStartTimeUtc">
        <strong>片段开始时间 (UTC):</strong> {{ segmentStartTimeUtc }}
      </p>
      <p><strong>当前播放绝对时间:</strong> {{ currentDisplayTime }}</p>
      <p><strong>视频时长:</strong> {{ duration > 0 ? duration.toFixed(2) + 's' : 'N/A' }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  segmentStartTimeUtc: {
    type: String,
    default: '',
  },
  segmentId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['timeupdate', 'ended', 'play', 'pause']);

const videoElement = ref(null);
const currentTime = ref(0);
const duration = ref(0);

const currentDisplayTime = computed(() => {
  if (!props.segmentStartTimeUtc) {
    return 'N/A';
  }
  // segmentStartTimeUtc 是 UTC 时间字符串，加上当前播放的秒数
  const absoluteTime = dayjs.utc(props.segmentStartTimeUtc).add(currentTime.value, 'second');
  // 转换为本地时间进行显示
  return absoluteTime.local().format('YYYY-MM-DD HH:mm:ss');
});

function handleTimeUpdate() {
  if (videoElement.value) {
    currentTime.value = videoElement.value.currentTime;
    emit('timeupdate', {
      currentPlaybackSeconds: currentTime.value,
      currentDisplayTime: currentDisplayTime.value,
    });
  }
}

function handleLoadedMetadata() {
    if (videoElement.value) {
        duration.value = videoElement.value.duration;
    }
}

function seekTo(seconds) {
  if (videoElement.value) {
    videoElement.value.currentTime = seconds;
    videoElement.value.play();
  }
}

// 暴露方法给父组件
defineExpose({
  seekTo,
});

watch(() => props.src, (newSrc) => {
  if (newSrc && videoElement.value) {
    videoElement.value.load();
    videoElement.value.play().catch(error => {
        console.warn("自动播放失败:", error);
    });
  }
});
</script>

<style scoped>
.video-player {
  margin-top: 20px;
  background-color: #000;
}
.video-element {
  width: 100%;
  max-height: 500px; /* 限制最大高度 */
}
.video-info {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  color: #333;
  font-size: 14px;
}
.video-info p {
  margin: 5px 0;
}
</style>