{
  // 设置默认文件编码为 UTF-8
  "files.encoding": "utf8",
  // 禁用自动猜测文件编码，防止误判
  "files.autoGuessEncoding": false,
  // 为 Windows 集成终端配置 UTF-8 启动参数
  "terminal.integrated.profiles.windows": {
    "PowerShell": {
      "source": "PowerShell",
      "icon": "terminal-powershell",
      "args": [
        "-NoExit",
        "-Command",
        "$OutputEncoding = [System.Text.Encoding]::UTF8"
      ]
    },
    "Command Prompt": {
      "path": "cmd.exe",
      "args": [
        "/K",
        "chcp 65001"
      ]
    }
  },
  // 在 Windows 上将 PowerShell 设置为默认终端
  "terminal.integrated.defaultProfile.windows": "PowerShell"
}