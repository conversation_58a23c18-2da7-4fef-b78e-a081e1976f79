"use strict";

const { logger } = require("ee-core/log");
const { getMainWindow } = require("ee-core/electron");
const { SerialPort } = require("serialport");
const WebSocket = require("ws");
const EventEmitter = require("events");
const MessageParser = require("../utils/messageParser");
const MessageEncoder = require("../utils/messageEncoder");
const globalStateManager = require("./globalStateManager");
const { SenderStatus } = require("../utils/constants");

/**
 * 双连接通信服务
 * 同时维护WebSocket和USB连接，使用统一的USB协议
 */
class DualConnectionService extends EventEmitter {
  static instance = null;

  static getInstance() {
    if (!DualConnectionService.instance) {
      DualConnectionService.instance = new DualConnectionService();
    }
    return DualConnectionService.instance;
  }

  constructor() {
    super();
    if (DualConnectionService.instance) {
      return DualConnectionService.instance;
    }

    // WebSocket连接
    this.websocket = {
      socket: null,
      url: "",
      isConnected: false,
      isConnecting: false,
      reconnectAttempts: 0,
      reconnectTimer: null,
      lastMessageTime: Date.now(),
    };

    // USB连接
    this.usb = {
      port: null,
      isConnected: false,
      isConnecting: false,
      reconnectAttempts: 0,
      reconnectTimer: null,
      lastMessageTime: Date.now(),
    };

    // 连接配置
    this.config = {
      maxReconnectAttempts: 50,
      reconnectInterval: 5000,
      healthCheckTimeout: 30000,
      messageInterval1100: 20000,
      usbBaudRate: 115200,
    };

    // 定时器
    this.messageTimer1100 = null;
    this.healthCheckTimer = null;

    // USB设备过滤器
    this.usbDeviceFilters = [
      { vendorId: "2207", productId: "0011" },
      { vendorId: "2207" },
      { vendorId: "0403" },
      { manufacturer: /rockchip/i },
      { manufacturer: /android/i },
    ];

    this.isStarted = false;
    DualConnectionService.instance = this;
  }

  /**
   * 启动双连接服务
   */
  async start() {
    if (this.isStarted) {
      logger.warn("[DualConnectionService] Service already started");
      return;
    }

    try {
      logger.info("[DualConnectionService] Starting dual connection service");

      // 同时启动WebSocket和USB连接
      await Promise.allSettled([this._startWebSocket(), this._startUSB()]);

      // 启动定时消息发送
      this._startPeriodicMessages();

      this.isStarted = true;
      logger.info("[DualConnectionService] Dual connection service started");

      // 通知前端服务状态
      this._notifyServiceStatus("started");
    } catch (error) {
      logger.error("[DualConnectionService] Failed to start service:", error);
      throw error;
    }
  }

  /**
   * 停止双连接服务
   */
  async stop() {
    if (!this.isStarted) {
      logger.warn("[DualConnectionService] Service not started");
      return;
    }

    try {
      logger.info("[DualConnectionService] Stopping dual connection service");

      // 停止定时器
      this._stopPeriodicMessages();

      // 同时断开WebSocket和USB连接
      await Promise.allSettled([this._stopWebSocket(), this._stopUSB()]);

      this.isStarted = false;
      logger.info("[DualConnectionService] Dual connection service stopped");

      // 通知前端服务状态
      this._notifyServiceStatus("stopped");
    } catch (error) {
      logger.error("[DualConnectionService] Failed to stop service:", error);
      throw error;
    }
  }
 
  /**
   * 停止WebSocket连接
   */
  async stopWebsocket() {
    await this._stopWebSocket();
  }

  /**
   * 停止USB连接
   */
  async stopUsb() {
    await this._stopUSB();
  }

  /**
   * 发送消息到指定连接或所有连接
   * @param {Buffer} message - 要发送的消息
   * @param {string} target - 目标连接 ('websocket', 'usb', 'all')
   */
  async sendMessage(message, target = "all") {
    if (target === "websocket" || target === "all") {
      if (this.websocket.isConnected && this.websocket.socket) {
        try {
          // WebSocket直接发送原始消息
          await this._sendWebSocketMessage(message);
        } catch (error) {
          console.log("发送WebSocket失败", error);
          logger.error("[DualConnectionService] WebSocket send failed:", error);
        }
      } else {
        console.log("ws not connect");
      }
    }

    if (target === "usb" || target === "all") {
      if (this.usb.isConnected && this.usb.port) {
        try {
          // USB消息需要额外封装
          const usbBuffer = MessageEncoder.packUsbMessage(message);
          await this._sendUSBMessage(usbBuffer);
        } catch (error) {
          console.log("发送USB失败", error);
          logger.error("[DualConnectionService] USB send failed:", error);
        }
      } else {
        console.log("usb not connect");
      }
    }
  }

  /**
   * 发送消息（自动路由）
   * @param {number} messageId - 消息ID
   * @param {object} body - 消息体
   */
  async sendMessageWithId(messageId, body) {
    console.log("准备发送消息", messageId, body);

    const msgBody = await MessageEncoder.buildBody(messageId, body);
    const head = {
      vehicleType: 0,
      dataUnitLength: msgBody.length,
      transmitCount: MessageEncoder.getTransmitCount(messageId),
    };

    const msgHead = MessageEncoder.buildHead(head);
    const mergedBuffer = Buffer.concat([msgHead, msgBody]);

    // 简单路由逻辑：1100和2048走双通道，其余只走网口
    const target = messageId === 1100 || messageId === 2048 ? "all" : "websocket";

    console.log("准备发送数据 send merged buffer", mergedBuffer);

    await this.sendMessage(mergedBuffer, target);
  }

  /**
   * 发送ID1100和ID2048消息（同时发送到两个连接）
   * @param {number} messageId - 消息ID (1100 或 2048)
   * @param {object} body - 消息体
   */
  async sendDualMessage(messageId, body) {
    if (messageId !== 1100 && messageId !== 2048) {
      throw new Error(`Message ID ${messageId} is not supported for dual sending`);
    }

    await this.sendMessageWithId(messageId, body);
  }

  /**
   * 发送ID20消息（仅WebSocket）
   * @param {object} body - 消息体
   */
  async sendMessageID20(body) {
    await this.sendMessageWithId(20, body);
  }

  /**
   * 获取消息的路由目标
   * @param {number} messageId - 消息ID
   * @returns {string} 路由目标 ('all' 或 'websocket')
   */
  getMessageTarget(messageId) {
    return messageId === 1100 || messageId === 2048 ? "all" : "websocket";
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isStarted: this.isStarted,
      websocket: {
        isConnected: this.websocket.isConnected,
        isConnecting: this.websocket.isConnecting,
        url: this.websocket.url,
        reconnectAttempts: this.websocket.reconnectAttempts,
        lastMessageTime: this.websocket.lastMessageTime,
      },
      usb: {
        isConnected: this.usb.isConnected,
        isConnecting: this.usb.isConnecting,
        portPath: this.usb.port ? this.usb.port.path : null,
        reconnectAttempts: this.usb.reconnectAttempts,
        lastMessageTime: this.usb.lastMessageTime,
      },
    };
  }

  /**
   * 检查服务健康状态
   */
  async isHealthy() {
    if (!this.isStarted) {
      return false;
    }

    // 至少有一个连接正常即认为健康
    const wsHealthy =
      this.websocket.isConnected && Date.now() - this.websocket.lastMessageTime < this.config.healthCheckTimeout;

    const usbHealthy = this.usb.isConnected && Date.now() - this.usb.lastMessageTime < this.config.healthCheckTimeout;

    return wsHealthy || usbHealthy;
  }

  // ==================== WebSocket 相关方法 ====================

  /**
   * 启动WebSocket连接
   */
  async _startWebSocket() {
    try {
      const androidUrl = await globalStateManager.get("pad.androidUrl");
      const androidWSPort = await globalStateManager.get("pad.androidWSPort");
      this.websocket.url = `ws://${androidUrl}:${androidWSPort}`;

      await this._connectWebSocket();
      logger.info(`[DualConnectionService] WebSocket connected: ${this.websocket.url}`);
    } catch (error) {
      logger.error("[DualConnectionService] WebSocket connection failed:", error);
      this._scheduleWebSocketReconnect();
    }
  }

  /**
   * 停止WebSocket连接
   */
  async _stopWebSocket() {
    if (this.websocket.reconnectTimer) {
      clearTimeout(this.websocket.reconnectTimer);
      this.websocket.reconnectTimer = null;
    }

    if (this.websocket.socket) {
      this.websocket.socket.close();
      this.websocket.socket = null;
    }

    this.websocket.isConnected = false;
    this.websocket.isConnecting = false;
    logger.info("[DualConnectionService] WebSocket disconnected");
  }

  /**
   * 连接WebSocket
   */
  async _connectWebSocket() {
    if (this.websocket.isConnected || this.websocket.isConnecting) {
      return;
    }

    this.websocket.isConnecting = true;

    return new Promise((resolve, reject) => {
      this.websocket.socket = new WebSocket(this.websocket.url);

      this.websocket.socket.onopen = () => {
        logger.info("[DualConnectionService] WebSocket connection opened");
        this.websocket.isConnected = true;
        this.websocket.isConnecting = false;
        this.websocket.reconnectAttempts = 0;

        if (this.websocket.reconnectTimer) {
          clearTimeout(this.websocket.reconnectTimer);
          this.websocket.reconnectTimer = null;
        }

        resolve();
      };

      this.websocket.socket.onmessage = (event) => {
        this._handleWebSocketMessage(event.data);
      };

      this.websocket.socket.onclose = () => {
        logger.info("[DualConnectionService] WebSocket connection closed");
        this.websocket.isConnected = false;
        this.websocket.isConnecting = false;
        this._scheduleWebSocketReconnect();
      };

      this.websocket.socket.onerror = (error) => {
        logger.error("[DualConnectionService] WebSocket error:", error);
        this.websocket.isConnecting = false;
        if (!this.websocket.isConnected) {
          reject(error);
        }
      };
    });
  }

  /**
   * 发送WebSocket消息
   */
  async _sendWebSocketMessage(message) {
    if (!this.websocket.isConnected || !this.websocket.socket || this.websocket.socket.readyState !== WebSocket.OPEN) {
      throw new Error("WebSocket connection not available");
    }

    return new Promise((resolve, reject) => {
      try {
        this.websocket.socket.send(message);
        logger.debug(`[DualConnectionService] WebSocket message sent: ${message.length} bytes`);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 处理WebSocket消息
   */
  async _handleWebSocketMessage(data) {
    try {
      this.websocket.lastMessageTime = Date.now();

      // 使用websocket协议解析消息
      const message = MessageParser.parse(data, "websocket");

      // 发送到前端
      this._notifyMessage(message, "websocket");

      console.log("收到ws消息", message);

      // 处理特定消息
      await this._handleMessage(message, "websocket");
    } catch (error) {
      logger.error("[DualConnectionService] Failed to parse WebSocket message:", error);
    }
  }

  /**
   * 安排WebSocket重连
   */
  _scheduleWebSocketReconnect() {
    if (this.websocket.reconnectAttempts >= this.config.maxReconnectAttempts) {
      logger.warn("[DualConnectionService] WebSocket max reconnect attempts reached");
      return;
    }

    if (this.websocket.reconnectTimer) {
      return;
    }

    this.websocket.reconnectAttempts++;

    this.websocket.reconnectTimer = setTimeout(async () => {
      this.websocket.reconnectTimer = null;
      logger.info(
        `[DualConnectionService] WebSocket reconnecting (${this.websocket.reconnectAttempts}/${this.config.maxReconnectAttempts})`
      );

      try {
        await this._connectWebSocket();
      } catch (error) {
        logger.error("[DualConnectionService] WebSocket reconnect failed:", error);
        this._scheduleWebSocketReconnect();
      }
    }, this.config.reconnectInterval);
  }

  // ==================== USB 相关方法 ====================

  /**
   * 启动USB连接
   */
  async _startUSB() {
    try {
      const devicePath = await this._findUSBDevice();
      if (!devicePath) {
        throw new Error("No compatible USB device found");
      }

      await this._connectUSB(devicePath);
      logger.info(`[DualConnectionService] USB connected: ${devicePath}`);
    } catch (error) {
      logger.error("[DualConnectionService] USB connection failed:", error);
      this._scheduleUSBReconnect();
    }
  }

  /**
   * 停止USB连接
   */
  async _stopUSB() {
    if (this.usb.reconnectTimer) {
      clearTimeout(this.usb.reconnectTimer);
      this.usb.reconnectTimer = null;
    }

    if (this.usb.port && this.usb.port.isOpen) {
      try {
        await new Promise((resolve, reject) => {
          this.usb.port.close((error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      } catch (error) {
        logger.error("[DualConnectionService] Error closing USB port:", error);
      }
    }

    this.usb.port = null;
    this.usb.isConnected = false;
    this.usb.isConnecting = false;
    logger.info("[DualConnectionService] USB disconnected");
  }

  /**
   * 查找USB设备
   */
  async _findUSBDevice() {
    try {
      const ports = await SerialPort.list();
      logger.info(`[DualConnectionService] Found ${ports.length} serial ports`);

      // 通过设备过滤器匹配
      for (const filter of this.usbDeviceFilters) {
        const matchedPort = ports.find((port) => {
          if (filter.vendorId && port.vendorId !== filter.vendorId) return false;
          if (filter.productId && port.productId !== filter.productId) return false;
          if (filter.manufacturer && !filter.manufacturer.test(port.manufacturer || "")) return false;
          return true;
        });

        if (matchedPort) {
          logger.info(`[DualConnectionService] Found matching USB device: ${matchedPort.path}`);
          return matchedPort.path;
        }
      }

      // 尝试使用配置中的路径
      const configPath = await globalStateManager.get("usb.devicePath");
      if (configPath) {
        const configPort = ports.find((port) => port.path === configPath);
        if (configPort) {
          logger.info(`[DualConnectionService] Using configured USB device: ${configPath}`);
          return configPath;
        }
      }

      // 开发模式下使用第一个可用端口
      if (process.env.NODE_ENV === "development" && ports.length > 0) {
        logger.warn(`[DualConnectionService] Using first available USB port for development: ${ports[0].path}`);
        return ports[0].path;
      }

      return null;
    } catch (error) {
      logger.error("[DualConnectionService] Error listing serial ports:", error);
      throw error;
    }
  }

  /**
   * 连接USB设备
   */
  async _connectUSB(devicePath) {
    if (this.usb.isConnected || this.usb.isConnecting) {
      return;
    }

    this.usb.isConnecting = true;

    return new Promise((resolve, reject) => {
      this.usb.port = new SerialPort({
        path: devicePath,
        baudRate: this.config.usbBaudRate,
        dataBits: 8,
        stopBits: 1,
        parity: "none",
        autoOpen: false,
        highWaterMark: 64 * 1024,
      });

      this.usb.port.on("open", () => {
        logger.info(`[DualConnectionService] USB port opened: ${devicePath}`);
        this.usb.isConnected = true;
        this.usb.isConnecting = false;
        this.usb.reconnectAttempts = 0;

        if (this.usb.reconnectTimer) {
          clearTimeout(this.usb.reconnectTimer);
          this.usb.reconnectTimer = null;
        }

        resolve();
      });

      this.usb.port.on("error", (error) => {
        logger.error("[DualConnectionService] USB port error:", error);
        this.usb.isConnecting = false;
        if (!this.usb.isConnected) {
          reject(error);
        }
      });

      this.usb.port.on("close", () => {
        logger.info("[DualConnectionService] USB port closed");
        this.usb.isConnected = false;
        this.usb.isConnecting = false;
        this._scheduleUSBReconnect();
      });

      this.usb.port.on("data", (data) => {
        this._handleUSBData(data);
      });

      this.usb.port.open();
    });
  }

  /**
   * 发送USB消息
   */
  async _sendUSBMessage(message) {
    if (!this.usb.isConnected || !this.usb.port || !this.usb.port.isOpen) {
      throw new Error("USB connection not available");
    }

    return new Promise((resolve, reject) => {
      this.usb.port.write(message, (error) => {
        if (error) {
          reject(error);
        } else {
          logger.debug(`[DualConnectionService] USB message sent: ${message.length} bytes`);
          resolve();
        }
      });
    });
  }

  /**
   * 处理USB数据
   */
  _handleUSBData(data) {
    try {
      this.usb.lastMessageTime = Date.now();
      logger.debug(`[DualConnectionService] USB received ${data.length} bytes`);

      // 使用USB协议解析消息
      const message = MessageParser.parse(data, "usb");

      console.log("收到usb消息", message);

      if (message) {
        // 发送到前端
        this._notifyMessage(message, "usb");

        // 处理特定消息
        this._handleMessage(message, "usb");
      }
    } catch (error) {
      logger.error("[DualConnectionService] Failed to parse USB data:", error);
    }
  }

  /**
   * 安排USB重连
   */
  _scheduleUSBReconnect() {
    if (this.usb.reconnectAttempts >= this.config.maxReconnectAttempts) {
      logger.warn("[DualConnectionService] USB max reconnect attempts reached");
      return;
    }

    if (this.usb.reconnectTimer) {
      return;
    }

    this.usb.reconnectAttempts++;

    this.usb.reconnectTimer = setTimeout(async () => {
      this.usb.reconnectTimer = null;
      logger.info(
        `[DualConnectionService] USB reconnecting (${this.usb.reconnectAttempts}/${this.config.maxReconnectAttempts})`
      );

      try {
        await this._startUSB();
      } catch (error) {
        logger.error("[DualConnectionService] USB reconnect failed:", error);
        this._scheduleUSBReconnect();
      }
    }, this.config.reconnectInterval);
  }

  // ==================== 消息处理相关方法 ====================

  /**
   * 处理接收到的消息
   */
  async _handleMessage(message, source) {
    try {
      switch (message.id) {
        case 1100:
        case 2048:
          logger.debug(`[DualConnectionService] Heartbeat message ${message.id} from ${source}`);
          break;
        case 20:
          await this._handleMessageID20(message, source);
          break;
        case 11:
          await this._handleMessageID11(message, source);
          break;
        default:
          logger.debug(`[DualConnectionService] Message ID ${message.id} from ${source}`);
          break;
      }
    } catch (error) {
      logger.error(`[DualConnectionService] Error handling message from ${source}:`, error);
    }
  }

  /**
   * 处理ID为20的消息
   */
  async _handleMessageID20(message, source) {
    logger.debug(`[DualConnectionService] Handling message ID 20 from ${source}`);

    if (message.jsonData && message.jsonData.page === 300 && message.jsonData.payload) {
      try {
        await globalStateManager.setAll(message.jsonData.payload);
        logger.info(`[DualConnectionService] Global state updated from ${source} message`);
      } catch (error) {
        logger.error(`[DualConnectionService] Error updating global state from ${source}:`, error);
      }
    }
  }

  /**
   * 处理ID为11的消息
   */
  async _handleMessageID11(message, source) {
    logger.debug(`[DualConnectionService] Handling message ID 11 from ${source}`);

    // 更新pad状态
    if (message["senderStatus"]) {
      let status = 8;
      switch (message["senderStatus"]) {
        case SenderStatus.INITIALIZE:
          status = SenderStatus.INITIALIZE;
          break;
        case SenderStatus.WAITING:
        case SenderStatus.DEBUGGING:
        case SenderStatus.ASYNC_MONITORING:
          status = SenderStatus.WAITING;
          break;
        case SenderStatus.LOGIN:
        case SenderStatus.REMOTE_CONTROL:
        case SenderStatus.LOCKED:
        case SenderStatus.FAILSAFE:
        case SenderStatus.LOOPBACK_TEST:
          status = SenderStatus.REMOTE_CONTROL;
          break;
      }

      const currentStatus = await globalStateManager.get("pad.status");
      if (currentStatus !== status) {
        await globalStateManager.set("pad.status", status);
      }
    }

    // 发送心跳响应（只通过接收到消息的连接回复）
    await this._sendHeartbeatResponse(message, source);
  }

  /**
   * 发送心跳响应
   */
  async _sendHeartbeatResponse(message, source) {
    try {
      const padStatus = await globalStateManager.get("pad.status");

      const body = {
        senderStatus: padStatus,
        confirmedTransmitCount: message.transmitCount,
      };

      const msgBody = await MessageEncoder.buildBody(12, body);
      const head = {
        vehicleType: 0,
        dataUnitLength: msgBody.length,
        transmitCount: MessageEncoder.getTransmitCount(11),
      };

      const msgHead = MessageEncoder.buildHead(head);
      const mergedBuffer = Buffer.concat([msgHead, msgBody]);

      // 只通过接收到消息的连接回复
      await this.sendMessage(mergedBuffer, source);
      logger.debug(`[DualConnectionService] Heartbeat response sent via ${source}`);
    } catch (error) {
      logger.error(`[DualConnectionService] Failed to send heartbeat response via ${source}:`, error);
    }
  }

  // ==================== 定时任务相关方法 ====================

  /**
   * 启动定时消息发送
   */
  _startPeriodicMessages() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
    }

    // 定时发送ID1100消息到所有连接
    this.messageTimer1100 = setInterval(async () => {
      try {
        const body = {
          vehicleSubType: 0,
          tabletStatus: 64, // 64远控
          failureLevel: 255, // 255没有失效
        };

        await this.sendDualMessage(1100, body);
      } catch (error) {
        logger.error("[DualConnectionService] Failed to send periodic message:", error);
      }
    }, this.config.messageInterval1100);

    // 延迟获取所有数据
    setTimeout(() => {
      this._getAllDataFromAndroid();
    }, 5000);
  }

  /**
   * 停止定时消息发送
   */
  _stopPeriodicMessages() {
    if (this.messageTimer1100) {
      clearInterval(this.messageTimer1100);
      this.messageTimer1100 = null;
    }
  }

  /**
   * 获取所有Android数据
   */
  async _getAllDataFromAndroid() {
    try {
      const body = {
        communicationType: 2,
        jsonData: {
          page: 300,
          type: "params",
          payload: {
            route: "all",
          },
        },
      };

      await this.sendMessageID20(body);
    } catch (error) {
      logger.error("[DualConnectionService] Failed to get all data from Android:", error);
    }
  }

  // ==================== 通知相关方法 ====================

  /**
   * 通知前端消息
   */
  _notifyMessage(message, source) {
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("ipc-message", {
        ...message,
        connectionType: source,
      });
    }
  }

  /**
   * 通知前端服务状态
   */
  _notifyServiceStatus(status) {
    const mainWindow = getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("connection-status-changed", {
        status,
        timestamp: Date.now(),
        connectionStatus: this.getConnectionStatus(),
      });
    }
  }
}

module.exports = DualConnectionService.getInstance();
